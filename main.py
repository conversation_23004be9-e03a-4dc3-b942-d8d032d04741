import win32api
import time
import json
from paddleocr import PaddleOCR
import pyautogui
import numpy as np
from PIL import ImageGrab, Image
import cv2
import os
import sys
from pathlib import Path
import warnings

# 设置PyAutoGUI的安全设置
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.3

# 设置图片目录
IMAGE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images')

# 检查所需的图片文件
REQUIRED_IMAGES = {
    'tuikuan.png': '退款按钮图片',
    'denglu.png': '登录按钮图片',
    'chaxun.png': '查询按钮图片',
    'wancheng.png': '完成标志图片'
}

# 忽略特定的警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*No ccache found.*")

def get_image_path(image_name):
    """获取图片的完整路径"""
    path = os.path.join(IMAGE_DIR, image_name)
    if not os.path.exists(path):
        print(f"警告: 图片文件不存在: {path}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"图片目录: {IMAGE_DIR}")
        print("目录内容:")
        try:
            for file in os.listdir(IMAGE_DIR):
                print(f"- {file}")
        except Exception as e:
            print(f"无法列出目录内容: {e}")
    return path

def verify_image(image_path):
    """验证图片文件是否可用"""
    try:
        # 尝试用PIL打开并转换为numpy数组
        with Image.open(image_path) as img:
            print(f"PIL可以打开图片: {image_path}")
            print(f"图片格式: {img.format}")
            print(f"图片大小: {img.size}")
            print(f"图片模式: {img.mode}")
            
            # 将图片转换为RGB模式（如果是RGBA，去掉alpha通道）
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            
            # 转换为numpy数组
            img_array = np.array(img)
            print(f"转换为numpy数组成功，形状: {img_array.shape}")
            return True
    except Exception as e:
        print(f"验证图片时出错: {e}")
        return False

def check_required_files():
    """检查所需的文件是否存在"""
    if not os.path.exists(IMAGE_DIR):
        print(f"\n错误: 图片目录不存在: {IMAGE_DIR}")
        print(f"当前工作目录: {os.getcwd()}")
        return False

    missing_files = []
    existing_files = []
    for filename, description in REQUIRED_IMAGES.items():
        file_path = get_image_path(filename)
        if not os.path.exists(file_path):
            missing_files.append(f"{filename} ({description})")
        else:
            existing_files.append(filename)
            # 验证图片
            print(f"\n验证图片: {filename}")
            verify_image(file_path)
    
    print("\n文件检查结果:")
    print(f"图片目录: {IMAGE_DIR}")
    
    if existing_files:
        print("\n已找到的文件:")
        for file in existing_files:
            print(f"- {file}")
    
    if missing_files:
        print("\n缺少的文件:")
        for file in missing_files:
            print(f"- {file}")
        return False
    return True

# OCR初始化
def init_ocr():
    """初始化OCR模型"""
    # 检查多个可能的模型路径
    possible_paths = [
        # PaddleX路径
        os.path.join(os.path.expanduser('~'), '.paddlex', 'official_models'),
        # 用户安装路径
        os.path.join(os.path.expanduser('~'), '.paddleocr'),
        # 全局安装路径
        os.path.join(os.path.dirname(os.__file__), 'site-packages', 'paddleocr', 'inference'),
    ]

    print("\n正在初始化OCR模型...")
    print("检查可能的模型路径:")
    
    # 查找已存在的模型路径
    det_path = None
    rec_path = None
    
    for base_path in possible_paths:
        print(f"\n检查目录: {base_path}")
        if os.path.exists(base_path):
            print("目录存在，列出内容:")
            try:
                for item in os.listdir(base_path):
                    print(f"- {item}")
                
                # 检查检测模型
                det_model = os.path.join(base_path, 'PP-OCRv5_server_det')
                if os.path.exists(det_model):
                    model_files = os.listdir(det_model)
                    if 'inference.pdiparams' in model_files:
                        det_path = det_model
                        print(f"找到检测模型: {det_path}")
                        print("模型文件列表:")
                        for f in model_files:
                            print(f"  - {f}")
                
                # 检查识别模型
                rec_model = os.path.join(base_path, 'PP-OCRv5_server_rec')
                if os.path.exists(rec_model):
                    model_files = os.listdir(rec_model)
                    if 'inference.pdiparams' in model_files:
                        rec_path = rec_model
                        print(f"找到识别模型: {rec_path}")
                        print("模型文件列表:")
                        for f in model_files:
                            print(f"  - {f}")
            except Exception as e:
                print(f"无法列出目录内容: {e}")
        else:
            print("目录不存在")
        
        if det_path and rec_path:
            break

    if det_path and rec_path:
        print(f"\n使用已有模型文件:")
        print(f"检测模型路径: {det_path}")
        print(f"识别模型路径: {rec_path}")
    else:
        print("\n未找到完整的模型文件，将使用默认配置...")
        if not det_path:
            print("缺少检测模型")
        if not rec_path:
            print("缺少识别模型")

    return PaddleOCR(
        lang="ch",  # 中文模型
        device="cpu",  # 使用CPU推理
        use_doc_orientation_classify=False,  # 关闭文档方向分类
        use_doc_unwarping=False,  # 关闭文档形变校正
        use_textline_orientation=False,  # 关闭文本行方向分类
        text_det_limit_side_len=960,  # 限制最长边为960
        text_det_limit_type="max",  # 限制类型为最大边
        text_det_box_thresh=0.5,  # 检测框阈值
        text_det_thresh=0.3,  # 检测阈值
        text_det_unclip_ratio=2.0,  # 文本框扩张比例
        text_rec_score_thresh=0.3,  # 识别阈值
        enable_mkldnn=True,  # 启用mkldnn加速
        cpu_threads=10,  # CPU线程数
        # 使用新的参数名称
        text_detection_model_dir=det_path,
        text_recognition_model_dir=rec_path
    )

# OCR实例 - 延迟初始化，避免在模块导入时就初始化
ocr = None

def get_ocr_instance():
    """获取OCR实例，使用单例模式延迟初始化"""
    global ocr
    if ocr is None:
        print("首次使用OCR，正在初始化...")
        ocr = init_ocr()
        print("OCR初始化完成")
    return ocr

# format_ocr_result 函数已删除，直接使用PaddleOCR 3.x的原生格式

def read_json(filename):
    """读取JSON文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('accounts', [])
    except FileNotFoundError:
        return []

def save_json(accounts, filename):
    """保存JSON文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({'accounts': accounts}, f, ensure_ascii=False, indent=2)

def smooth_move_mouse(x2, y2, duration=0.4, intervals=50):
    """平滑地将鼠标从当前位置移动到目标位置
    
    Args:
        x2: 目标x坐标
        y2: 目标y坐标
        duration: 移动持续时间(秒)
        intervals: 移动过程分成多少段
    """
    x1, y1 = win32api.GetCursorPos()
    distance_x = x2 - x1
    distance_y = y2 - y1
    
    for n in range(intervals + 1):
        progress = n / intervals
        # 使用缓动函数使移动更自然
        eased_progress = ease_out_quad(progress)
        current_x = x1 + distance_x * eased_progress
        current_y = y1 + distance_y * eased_progress
        win32api.SetCursorPos((int(current_x), int(current_y)))
        time.sleep(duration / intervals)

def ease_out_quad(t):
    """缓动函数，使移动更自然"""
    return -t * (t - 2)

def click_image(image_name, confidence=0.7, max_retries=3, region=None):
    """查找并点击指定图片，支持重试"""
    image_path = get_image_path(image_name)
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return False

    print(f"\n尝试查找并点击图片: {image_name}")
    print(f"图片路径: {image_path}")
    print(f"置信度: {confidence}")
    if region:
        print(f"搜索区域: {region}")

    try:
        # 使用PIL加载图片
        template = Image.open(image_path)
        if template.mode == 'RGBA':
            template = template.convert('RGB')
    except Exception as e:
        print(f"加载图片时出错: {e}")
        return False

    for attempt in range(max_retries):
        try:
            print(f"尝试第 {attempt + 1} 次...")
            # 首先尝试定位图片
            if region:
                location = pyautogui.locateCenterOnScreen(
                    template,
                    confidence=confidence,
                    region=region
                )
            else:
                location = pyautogui.locateCenterOnScreen(
                    template,
                    confidence=confidence
                )

            if location:
                x, y = location
                print(f"找到图片，位置: ({x}, {y})")
                smooth_move_mouse(x, y)
                time.sleep(0.2)  # 稍微增加点击前的等待时间
                pyautogui.click()
                print("点击成功")
                return True
            else:
                print("未找到图片")
                # 保存当前屏幕截图以供调试
                if not region:
                    screen = pyautogui.screenshot()
                    debug_path = f"debug_{image_name}"
                    screen.save(debug_path)
                    print(f"已保存调试截图: {debug_path}")
                time.sleep(1)  # 短暂等待后重试
        except Exception as e:
            print(f"点击过程出错: {e}")
            import traceback
            print("错误详细信息:")
            print(traceback.format_exc())
    
    print(f"在 {max_retries} 次尝试后仍未成功点击图片")
    return False

def move_and_click(x, y, duration=0.4):
    """移动到指定坐标并点击"""
    smooth_move_mouse(x, y, duration)
    time.sleep(0.1)
    pyautogui.click()

def wait_for_image(image_name, confidence=0.7, timeout=30, check_interval=0.8, region=None):
    """等待图片出现，支持超时"""
    image_path = get_image_path(image_name)
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return False

    print(f"\n等待图片出现: {image_name}")
    print(f"超时时间: {timeout}秒")
    print(f"检查间隔: {check_interval}秒")
    if region:
        print(f"检查区域: {region}")

    try:
        # 使用PIL加载图片
        template = Image.open(image_path)
        if template.mode == 'RGBA':
            template = template.convert('RGB')
    except Exception as e:
        print(f"加载图片时出错: {e}")
        return False

    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            if region:
                if pyautogui.locateOnScreen(template, confidence=confidence, region=region):
                    print(f"找到图片 {image_name}")
                    return True
            else:
                if pyautogui.locateOnScreen(template, confidence=confidence):
                    print(f"找到图片 {image_name}")
                    return True
        except Exception as e:
            print(f"检查图片时出错: {e}")
        time.sleep(check_interval)
    
    print(f"等待超时，未找到图片 {image_name}")
    return False

def get_account_positions(region):
    """获取指定区域内的账号位置信息"""
    try:
        print(f"截取区域: {region}")
        # 截取指定区域的图像
        screenshot = ImageGrab.grab(bbox=region)
        
        # 保存截图以供调试
        debug_path = "debug_account_area.png"
        screenshot.save(debug_path)
        print(f"已保存账号区域截图: {debug_path}")
        
        # OCR识别
        ocr_instance = get_ocr_instance()
        results = ocr_instance.ocr(np.array(screenshot))

        # 调试：打印OCR原始结果
        print(f"OCR原始结果类型: {type(results)}")
        print(f"OCR原始结果长度: {len(results) if results else 'None'}")
        print(f"OCR原始结果内容: {results}")

        account_positions = []
        # PaddleOCR 3.x 返回的是字典格式，需要正确解析
        if results and len(results) > 0:
            # 获取第一页结果
            page_result = results[0]
            print(f"第一页结果类型: {type(page_result)}")

            # 检查是否是字典格式并包含必要的键
            if isinstance(page_result, dict) and 'rec_texts' in page_result and 'rec_scores' in page_result and 'rec_boxes' in page_result:
                texts = page_result['rec_texts']
                scores = page_result['rec_scores']
                boxes = page_result['rec_boxes']

                print(f"识别到 {len(texts)} 个文本")

                for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                    print(f"文本 {i}: '{text}', 置信度: {score:.4f}")

                    # 降低置信度阈值
                    if score > 0.1:
                        # 计算边界框中心点
                        # box格式: [left, top, right, bottom]
                        center_x = int((box[0] + box[2]) / 2)
                        center_y = int((box[1] + box[3]) / 2)

                        # 计算绝对坐标（相对于屏幕）
                        abs_center_x = center_x + region[0]
                        abs_center_y = center_y + region[1]

                        account_positions.append({
                            'name': text,
                            'position': (abs_center_x, abs_center_y)
                        })
                        print(f"✓ 添加账号: {text}, 位置: ({abs_center_x}, {abs_center_y}), 置信度: {score:.4f}")
                    else:
                        print(f"✗ 跳过低置信度文本: '{text}', 置信度: {score:.4f}")
            else:
                print("OCR结果格式不正确或缺少必要的键")
                # 尝试打印可用键
                if isinstance(page_result, dict):
                    print(f"可用键: {list(page_result.keys())}")
                elif hasattr(page_result, '__dict__'):
                    print(f"可用属性: {list(page_result.__dict__.keys())}")
        else:
            print("OCR结果为空或无效")
        
        return account_positions
    except Exception as e:
        print(f"获取账号位置时出错: {e}")
        import traceback
        print("错误详细信息:")
        print(traceback.format_exc())
        return []

def process_account(account_name, account_positions):
    """处理单个账号"""
    # 查找账号位置并点击
    for pos_info in account_positions:
        # 检查账号名称是否在识别到的文本中（部分匹配）
        if account_name in pos_info['name']:
            x, y = pos_info['position']
            print(f"找到匹配账号，完整文本: {pos_info['name']}")
            print(f"匹配的账号名称: {account_name}")
            smooth_move_mouse(x, y)
            time.sleep(1)
            pyautogui.click()
            print(f"点击账号: {account_name} 在位置: ({x}, {y})")
            break
    else:
        print(f"未找到账号: {account_name}")
        print("当前可用的账号列表:")
        for pos in account_positions:
            print(f"- {pos['name']}")
        return False

    # 点击登录按钮
    if not click_image("denglu.png", confidence=0.8):
        print("未找到登录按钮")
        return False

    print("点击登录按钮后等待 6 秒...")
    time.sleep(6)

    # 检测GUANBI.png，每隔0.5秒检测一次，最多检测4次（总共2秒）
    print("开始检测GUANBI.png...")
    guanbi_found = False
    for i in range(4):
        print(f"第 {i+1} 次检测GUANBI.png...")
        try:
            # 使用PIL加载图片
            guanbi_image_path = get_image_path("GUANBI.png")
            if os.path.exists(guanbi_image_path):
                template = Image.open(guanbi_image_path)
                if template.mode == 'RGBA':
                    template = template.convert('RGB')

                location = pyautogui.locateCenterOnScreen(template, confidence=0.8)
                if location:
                    x, y = location
                    print(f"找到GUANBI.png，位置: ({x}, {y})，立即点击")
                    smooth_move_mouse(x, y)
                    time.sleep(0.2)
                    pyautogui.click()
                    guanbi_found = True
                    break
                else:
                    print("未找到GUANBI.png")
            else:
                print(f"GUANBI.png文件不存在: {guanbi_image_path}")
        except Exception as e:
            print(f"检测GUANBI.png时出错: {e}")

        if i < 3:  # 不是最后一次检测时才等待
            time.sleep(0.5)

    if guanbi_found:
        print("已点击GUANBI.png，继续下一步")
    else:
        print("检测完成，未发现GUANBI.png，继续下一步")

    # 去除移动到(1700, 800)的步骤，直接执行下一步
    print(f"移动到坐标 (1610, 63) 并点击...")  # 修改坐标从1681改为1610
    move_and_click(1610, 63)
    print("等待 1 秒...")
    time.sleep(1)

    # 点击查询按钮
    if not click_image("chaxun.png", confidence=0.8):
        print("未找到查询按钮")
        return False

    # 移动鼠标到指定位置
    print("移动鼠标到坐标 (1586, 374)...")
    smooth_move_mouse(1586, 374)
    time.sleep(0.5)  # 添加短暂延时确保移动完成

    # 等待完成图标出现
    if not wait_for_image("wancheng.png", confidence=0.5, timeout=120):
        print("等待完成超时")
        return False

    # 执行最后的移动和点击
    print("等待 15 秒...")
    time.sleep(15)  # 修改为等待15秒
    print("15秒等待完成，移动到坐标 (1854, 112) 并直接点击...")
    move_and_click(1854, 112)  # 修改坐标并直接点击
    time.sleep(2)
    print("账号处理的最后步骤完成，准备返回主循环...")

    return True

def main(input_accounts=None):
    """主函数
    Args:
        input_accounts: 可选的账号列表，如果提供则使用此列表而不是从文件读取
    """
    print("程序启动，检查必需文件...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"PyAutoGUI版本: {pyautogui.__version__}")
    
    # 获取屏幕信息
    screen_size = pyautogui.size()
    print(f"屏幕分辨率: {screen_size.width}x{screen_size.height}")
    
    # 检查必需的文件
    if not check_required_files():
        print("\n请确保所有必需的图片文件都存在并且文件名正确。")
        print("程序即将退出...")
        return

    print("\n文件检查完成，等待3秒...")
    time.sleep(3)

    # 读取账号信息
    accounts = input_accounts if input_accounts is not None else read_json("ZHANGHAO.JSON")
    print(f"已加载 {len(accounts)} 个账号")
    
    # 首次点击退款按钮
    if not click_image("tuikuan.png", confidence=0.7, max_retries=5):
        print("未找到退款按钮，程序退出")
        return
    
    time.sleep(1)
    
    while True:
        # 点击指定坐标 - 修改为新坐标
        print("\n点击坐标(1400, 247)")
        move_and_click(1400, 247)

        # 等待2秒让界面加载
        print("等待2秒让界面加载...")
        time.sleep(2)

        # 获取账号列表区域的账号位置信息 - 修改OCR识别区域
        print("\n开始识别账号列表...")
        account_positions = get_account_positions((1340, 264, 1700, 640))
        if not account_positions:
            print("未识别到任何账号，程序退出")
            break
        
        # 打印识别到的所有账号
        print("\n识别到的所有账号:")
        for pos in account_positions:
            print(f"账号: {pos['name']}, 位置: {pos['position']}")
        
        # 查找下一个未处理的账号
        next_account = None
        for account in accounts:
            if not account.get('processed'):
                next_account = account
                break
        
        if not next_account:
            print("所有账号处理完成")
            break
        
        print(f"\n开始处理账号: {next_account['name']}")
        # 处理账号
        if process_account(next_account['name'], account_positions):
            next_account['processed'] = True
            if input_accounts is None:  # 只有在使用文件时才保存
                save_json(accounts, "ZHANGHAO.JSON")
            print(f"账号 {next_account['name']} 处理完成")

            # 在开始下一个账号前添加额外等待时间
            print("等待 3 秒后开始处理下一个账号...")
            time.sleep(3)

            # 继续下一个账号的处理，不需要重新点击退款按钮
            continue
        else:
            print(f"处理账号 {next_account['name']} 失败，程序退出")
            break

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序发生错误: {e}")
        import traceback
        print("\n错误详细信息:")
        print(traceback.format_exc())
        print("\n按回车键退出...")
        input() 
