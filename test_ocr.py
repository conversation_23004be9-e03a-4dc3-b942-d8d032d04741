#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR测试脚本 - 用于调试OCR功能
"""

import sys
import os
import numpy as np
from PIL import Image, ImageGrab

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import get_ocr_instance

def test_ocr_with_screenshot():
    """测试OCR功能，使用截图"""
    print("开始OCR测试...")
    
    # 截取指定区域
    region = (1290, 480, 1460, 800)
    print(f"截取区域: {region}")
    
    try:
        # 截取屏幕区域
        screenshot = ImageGrab.grab(bbox=region)
        print(f"截图成功，尺寸: {screenshot.size}")
        
        # 保存截图用于调试
        screenshot.save("test_ocr_screenshot.png")
        print("已保存测试截图: test_ocr_screenshot.png")
        
        # 转换为RGB格式（如果是RGBA）
        if screenshot.mode == 'RGBA':
            screenshot = screenshot.convert('RGB')
            print("已转换为RGB格式")
        
        # 获取OCR实例
        print("获取OCR实例...")
        ocr_instance = get_ocr_instance()
        
        # 进行OCR识别
        print("开始OCR识别...")
        results = ocr_instance.ocr(np.array(screenshot))
        
        # 打印详细结果
        print(f"\nOCR结果类型: {type(results)}")
        print(f"OCR结果长度: {len(results) if results else 'None'}")
        print(f"OCR结果是否为空: {results is None or len(results) == 0}")
        
        if results:
            print(f"\n详细OCR结果:")
            for i, page_result in enumerate(results):
                print(f"页面 {i}: {type(page_result)}, 长度: {len(page_result) if page_result else 'None'}")
                if page_result:
                    for j, line in enumerate(page_result):
                        print(f"  行 {j}: {line}")
        else:
            print("OCR结果为空")
            
    except Exception as e:
        print(f"OCR测试出错: {e}")
        import traceback
        print("错误详细信息:")
        print(traceback.format_exc())

def test_ocr_with_saved_image():
    """测试OCR功能，使用已保存的图片"""
    image_path = "debug_account_area.png"
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
        
    print(f"测试已保存的图片: {image_path}")
    
    try:
        # 加载图片
        image = Image.open(image_path)
        print(f"图片尺寸: {image.size}")
        print(f"图片模式: {image.mode}")
        
        # 转换为RGB格式（如果需要）
        if image.mode == 'RGBA':
            image = image.convert('RGB')
            print("已转换为RGB格式")
        
        # 获取OCR实例
        print("获取OCR实例...")
        ocr_instance = get_ocr_instance()
        
        # 进行OCR识别
        print("开始OCR识别...")
        results = ocr_instance.ocr(np.array(image))
        
        # 打印详细结果
        print(f"\nOCR结果类型: {type(results)}")
        print(f"OCR结果长度: {len(results) if results else 'None'}")
        
        if results:
            print(f"\n详细OCR结果:")
            for i, page_result in enumerate(results):
                print(f"页面 {i}: {type(page_result)}, 长度: {len(page_result) if page_result else 'None'}")
                if page_result:
                    for j, line in enumerate(page_result):
                        if len(line) >= 2:
                            box_points = line[0]
                            text_info = line[1]
                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]
                                print(f"  文本: '{text}', 置信度: {confidence:.4f}")
        else:
            print("OCR结果为空")
            
    except Exception as e:
        print(f"OCR测试出错: {e}")
        import traceback
        print("错误详细信息:")
        print(traceback.format_exc())

if __name__ == "__main__":
    print("=== OCR功能测试 ===")
    
    # 测试已保存的图片
    print("\n1. 测试已保存的图片:")
    test_ocr_with_saved_image()
    
    # 测试实时截图
    print("\n2. 测试实时截图:")
    print("请确保目标区域可见，按回车继续...")
    input()
    test_ocr_with_screenshot()
    
    print("\n测试完成")
