# 批量提取秒退订单工具

这是一个带有图形界面的批量提取秒退订单工具。

## 功能特点

- 现代化的图形用户界面
- 支持多账号批量处理
- 可选择性处理账号
- 实时显示处理进度
- 支持全选/取消全选

## 安装步骤

1. 确保已安装 Python 3.8 或更高版本
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 使用说明

1. 准备文件：
   - 确保 `ZHANGHAO.JSON` 文件存在并包含正确的账号信息
   - 确保 `images` 目录下有所需的图片文件：
     - tuikuan.png（退款按钮图片）
     - denglu.png（登录按钮图片）
     - chaxun.png（查询按钮图片）
     - wancheng.png（完成标志图片）

2. 运行程序：
   ```bash
   python ui.py
   ```

3. 使用界面：
   - 程序启动时会显示所有账号，默认全部选中
   - 可以手动取消勾选不需要处理的账号
   - 点击"全选/取消全选"按钮可以快速选择或取消选择所有账号
   - 点击"批量提取秒退订单"按钮开始处理选中的账号

## 注意事项

- 运行过程中请勿移动鼠标或操作键盘
- 确保屏幕分辨率正确（建议 1920x1080）
- 程序运行时会自动下载并使用 PaddleOCR 模型
- 首次运行可能需要等待模型下载完成

## 文件说明

- `ui.py`: 图形界面主程序
- `main.py`: 核心处理逻辑
- `ZHANGHAO.JSON`: 账号配置文件
- `images/`: 图片资源目录
- `requirements.txt`: 依赖包列表

## 常见问题

1. 如果遇到 OCR 模型下载问题：
   - 检查网络连接
   - 确保 Python 环境正确配置

2. 如果遇到图片识别问题：
   - 确保图片文件清晰可见
   - 检查图片文件是否完整

3. 如果遇到界面显示问题：
   - 确保安装了最新版本的 PyQt6
   - 检查系统分辨率设置

## 技术支持

如有问题，请提供以下信息：
- Python 版本
- 系统环境
- 错误信息截图
- 日志文件（如果有） 