# 应用流程修改说明

## 修改概述

根据您的要求，已对批量提取秒退订单工具的自动化流程进行了以下修改：

## 具体修改内容

### 1. A2-A3: 配置文件读取 - 默认勾选修改

**修改位置**: `ui.py` 第102-109行

**原始逻辑**: 
```python
checkbox.setChecked(True)  # 默认选中所有账号
```

**修改后逻辑**:
```python
# 只默认选中"诗蝶仙旗舰店"和"若花淑旗舰店"
if account['name'] in ['诗蝶仙旗舰店', '若花淑旗舰店']:
    checkbox.setChecked(True)
else:
    checkbox.setChecked(False)
```

**效果**: 启动应用后，只有"诗蝶仙旗舰店"和"若花淑旗舰店"被默认勾选，其他账号保留但不勾选。

### 2. A4-A5: 账号选择区域定位 - 坐标修改

**修改位置**: `main.py` 第562-563行

**原始坐标**: `(1360, 445)`
**修改后坐标**: `(1400, 247)`

```python
print("\n点击坐标(1400, 247)")
move_and_click(1400, 247)
```

### 3. A5-A6: OCR账号识别 - 识别区域修改

**修改位置**: `main.py` 第571行

**原始区域**: `(1290, 480, 1460, 800)`
**修改后区域**: `(1340, 264, 1700, 640)`

```python
account_positions = get_account_positions((1340, 264, 1700, 640))
```

**区域变化**:
- 左上角: (1290, 480) → (1340, 264)
- 右下角: (1460, 800) → (1700, 640)
- 识别区域向右上方移动并扩大

### 4. A9-A10: 导航操作 - 去除步骤

**修改位置**: `main.py` 第496-500行

**去除的步骤**:
```python
# 已删除以下代码
print(f"移动到坐标 (1700, 800) 并点击...")
move_and_click(1700, 800)
print("等待 1 秒...")
time.sleep(1)
```

**保留并修改的步骤**:
```python
print(f"移动到坐标 (1610, 63) 并点击...")  # 从1681改为1610
move_and_click(1610, 63)
```

### 5. A12-A13: 完成后操作 - 坐标修改

**修改位置**: `main.py` 第520-521行

**原始操作**:
```python
smooth_move_mouse(1800, 130)  # 移动但不点击
time.sleep(1)
move_and_click(1800, 342)     # 再移动并点击
```

**修改后操作**:
```python
print("移动到坐标 (1854, 112) 并直接点击...")
move_and_click(1854, 112)  # 直接移动并点击
```

**变化**:
- 坐标从 (1800, 130) → (1854, 112)
- 简化操作：直接点击，不再分两步

## 修改后的完整流程

### A1-A2: 应用启动与初始化
- ✅ 保持不变

### A2-A3: 配置文件读取
- ✅ **已修改**: 默认只勾选"诗蝶仙旗舰店"和"若花淑旗舰店"

### A3-A4: 首次界面操作
- ✅ 保持不变

### A4-A5: 账号选择区域定位
- ✅ **已修改**: 坐标从 (1360, 445) → (1400, 247)

### A5-A6: OCR账号识别
- ✅ **已修改**: 识别区域从 (1290,480,1460,800) → (1340,264,1700,640)

### A6-A7: 智能账号匹配
- ✅ 保持不变

### A7-A8: 账号登录操作
- ✅ 保持不变

### A8-A9: 登录后处理
- ✅ 保持不变

### A9-A10: 导航操作
- ✅ **已修改**: 去除 (1700, 800) 点击步骤，坐标从 (1681, 63) → (1610, 63)

### A10-A11: 查询操作
- ✅ 保持不变

### A11-A12: 等待完成检测
- ✅ 保持不变

### A12-A13: 完成后操作
- ✅ **已修改**: 坐标从 (1800, 130) → (1854, 112)，简化为直接点击

### A13-A14: 状态更新与循环判断
- ✅ 保持不变

### A14-A15: 循环继续或结束
- ✅ 保持不变

## 测试建议

1. **界面测试**: 启动 `python ui.py` 确认只有指定的两个账号被默认勾选
2. **坐标测试**: 运行程序确认新坐标位置正确
3. **OCR测试**: 运行 `python test_ocr.py` 测试新的识别区域
4. **完整流程测试**: 选择一个账号进行完整的自动化流程测试

## 注意事项

- 所有修改都保持了原有的错误处理机制
- 代码结构和逻辑保持不变，只修改了指定的参数和流程
- 建议在实际环境中测试新坐标的准确性
- 如需进一步调整坐标，可以通过修改对应的数值来实现
