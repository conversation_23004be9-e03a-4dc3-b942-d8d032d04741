import os
import logging
from paddleocr import PaddleOCR

# 1. 设置环境变量控制日志
os.environ["PADDLEOCR_SHOW_LOG"] = "0"

# 2. 配置logging
logging.basicConfig(
    level=logging.ERROR,  # 只显示ERROR级别日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='ocr.log'  # 将日志输出到文件
)

# 3. 实例化OCR时禁用不必要的模型和日志
ocr = PaddleOCR(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False, 
    use_textline_orientation=False,
    show_log=False
)

# 4. 执行OCR
img_path = "test.jpg"
result = ocr.ocr(img_path)

# 5. 只打印关键信息
for idx, line in enumerate(result):
    print(f'Line {idx + 1}: {line[1][0]}') 
 