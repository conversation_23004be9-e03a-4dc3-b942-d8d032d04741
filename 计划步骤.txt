这是一个会循环操作的脚本，启动后，等待3秒钟，开始执行——


A1-A2 点击TUIKUAN.png
A2-A3 等待1秒
A3-A4 移动到1360,445 点击
A5-A6 启动OCR，识图区域左上角1290,480 右下角1460,800
A6-A7 读取我们的配置文件——ZHANGHAO.JSON(该文件未创建)
该文件保存了6个账号——
诗蝶仙旗舰店
亿米奴企业店
唐伯虎点蚊香
忘记美丽吧
江江江吖吖
若花淑旗舰店

A5-A6 OCR识别步骤会识别到这6个账号，循序JSON文件的顺序，点击第一个账号——诗蝶仙旗舰店
A6-A7 等待0.5秒
A7-A8 点击DENGLU.PNG
A8-A9 等待1.5秒
A9-A10 移动到1700,800 点击
A10-A11 等待0.5秒
A11-A12 移动到1734,63 点击
A12-A13 订单0.5秒
A13-A14  点击CHAXUN.PNG

A14-A15 每隔0.8秒检测一下有没有出现WANCHENG.png
A15-A16 如果没有出现，继续等待
A16-A17 如果出现，移动到1800,130 但不要点击，等待0.3秒，移动到1800,342 点击
A17-A18 等待1.5秒

随后，页面会回到A3的步骤——
我们开始进入循环，继续执行——
A3-A4 移动到1360,445 点击
A5-A6 启动OCR，识图区域左上角1290,480 右下角1460,800
A6-A7 读取我们的配置文件——ZHANGHAO.JSON(该文件未创建)
该文件保存了6个账号——
诗蝶仙旗舰店
亿米奴企业店
唐伯虎点蚊香
忘记美丽吧
江江江吖吖
若花淑旗舰店

关键的步骤在于——
A5-A6 OCR识别步骤会识别到这6个账号，循序JSON文件的顺序，点击第二个账号——亿米奴企业店

.....

直到应用完全操作完毕6个账号



请你详解一下有关于 ZHANGHAO.JSON 的处理方式——

在A5-A6 启动OCR，识图区域左上角1290,480 右下角1460,800

OCR会识别到6个账号位于页面的哪些位置——

我们读取ZHANGHAO.JSON的目的是，为了避免重复登录账号

读取ZHANGHAO.JSON，可以获悉第1个要点击的账号是什么名称

我们将依照OCR识别的坐标来点击第1个账号。

在循环处理时，它可以获悉第2个要点击的账号是什么名称

同样，我们将依照OCR识别的坐标来点击第2个账号。


当前的代码，是不是这个逻辑？


第一个问题——鼠标移动过于卡顿，请更换为流程自然的WIN32API
第二个问题——OCR识别存在问题，我们使用的是PALLDEOCR3.0.1，你需要掌握这个版本的语法，请检索官方项目