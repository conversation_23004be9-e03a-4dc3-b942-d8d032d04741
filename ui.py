import sys
import json
import os
import ctypes
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QCheckBox, QPushButton, QLabel, QScrollArea, 
                            QTextEdit, QMessageBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor
import main  # 导入原有的main.py

def set_dpi_awareness():
    """设置DPI感知"""
    try:
        # Windows 10 1607及以上版本
        ctypes.windll.shcore.SetProcessDpiAwareness(2)  # PROCESS_PER_MONITOR_DPI_AWARE
    except AttributeError:
        try:
            # Windows 8.1及以上版本
            ctypes.windll.user32.SetProcessDPIAware()
        except AttributeError:
            pass

# 在程序启动时设置DPI感知
set_dpi_awareness()

class WorkerThread(QThread):
    """工作线程，用于执行批量处理任务"""
    progress = pyqtSignal(str)  # 用于发送进度信息
    finished = pyqtSignal()     # 用于发送完成信号
    error = pyqtSignal(str)     # 用于发送错误信息

    def __init__(self, selected_accounts):
        super().__init__()
        self.selected_accounts = selected_accounts

    def run(self):
        try:
            # 修改原main.py中的accounts为选中的账号
            accounts = []
            for account in self.selected_accounts:
                accounts.append({"name": account, "processed": False})
            
            # 发送开始处理的信息
            self.progress.emit("开始处理选中的账号...")
            for account in accounts:
                self.progress.emit(f"准备处理账号: {account['name']}")
            
            # 执行原有的main函数逻辑，使用正确的函数调用方式
            from main import main as process_main
            process_main(input_accounts=accounts)
            
            self.progress.emit("所有账号处理完成！")
            self.finished.emit()
        except Exception as e:
            import traceback
            error_msg = f"发生错误:\n{str(e)}\n\n详细信息:\n{traceback.format_exc()}"
            self.error.emit(error_msg)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.WindowType.Window | 
                          Qt.WindowType.WindowMinimizeButtonHint | 
                          Qt.WindowType.WindowCloseButtonHint)
        self.initUI()

    def initUI(self):
        # 设置窗口标题和大小 - 减少30%宽度和高度
        self.setWindowTitle('批量提取秒退订单工具')
        self.setMinimumSize(420, 560)  # 原来600x800，减少30%后为420x560
        
        # 创建主窗口部件和布局 - 减少边距
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(6)  # 减少组件间距
        layout.setContentsMargins(12, 12, 12, 12)  # 减少边距

        # 添加标题标签
        title = QLabel('账号列表')
        title.setFont(QFont('Microsoft YaHei', 14, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 创建滚动区域 - 去除账号间隔
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(0)  # 去除账号之间的间隔
        scroll_layout.setContentsMargins(0, 0, 0, 0)  # 去除内边距
        scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # 读取账号列表
        self.checkboxes = []
        try:
            with open('ZHANGHAO.JSON', 'r', encoding='utf-8') as f:
                data = json.load(f)
                accounts = data.get('accounts', [])
                
                for account in accounts:
                    checkbox = QCheckBox(account['name'])
                    checkbox.setFont(QFont('Microsoft YaHei', 11))  # 增大字体两号：9->11
                    # 只默认选中"诗蝶仙旗舰店"和"若花淑旗舰店"
                    if account['name'] in ['诗蝶仙旗舰店', '若花淑旗舰店']:
                        checkbox.setChecked(True)
                    else:
                        checkbox.setChecked(False)
                    checkbox.setStyleSheet("""
                        QCheckBox {
                            padding: 6px 8px;
                            margin: 0px;
                            border-radius: 4px;
                            background-color: transparent;
                            border: none;
                            color: #333333;
                        }
                        QCheckBox:hover {
                            background-color: #f5f5f5;
                        }
                        QCheckBox:checked {
                            color: #1976d2;
                        }
                        QCheckBox:checked:hover {
                            background-color: #f5f5f5;
                        }
                        QCheckBox::indicator {
                            width: 18px;
                            height: 18px;
                            border-radius: 3px;
                            border: 2px solid #cccccc;
                            background-color: white;
                            margin-right: 6px;
                        }
                        QCheckBox::indicator:hover {
                            border-color: #1976d2;
                        }
                        QCheckBox::indicator:checked {
                            background-color: #1976d2;
                            border-color: #1976d2;
                            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxNCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMNSA5TDIgNiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                        }
                        QCheckBox::indicator:checked:hover {
                            background-color: #1565c0;
                            border-color: #1565c0;
                        }
                    """)
                    self.checkboxes.append(checkbox)
                    scroll_layout.addWidget(checkbox)

        except FileNotFoundError:
            error_label = QLabel('未找到ZHANGHAO.JSON文件')
            error_label.setStyleSheet('color: red;')
            scroll_layout.addWidget(error_label)

        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)

        # 添加全选/取消全选按钮 - 减少尺寸
        select_all_btn = QPushButton('全选/取消全选')
        select_all_btn.setFont(QFont('Microsoft YaHei', 9))
        select_all_btn.clicked.connect(self.toggle_all)
        select_all_btn.setStyleSheet("""
            QPushButton {
                padding: 6px;
                border-radius: 3px;
                background-color: #e0e0e0;
                border: none;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #d0d0d0;
            }
        """)
        layout.addWidget(select_all_btn)

        # 添加日志显示区域 - 减少高度30%
        log_label = QLabel('运行日志')
        log_label.setFont(QFont('Microsoft YaHei', 10, QFont.Weight.Bold))  # 减小字体
        layout.addWidget(log_label)

        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setFont(QFont('Microsoft YaHei', 8))  # 减小字体
        self.log_area.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        self.log_area.setMinimumHeight(140)  # 从200减少到140，减少30%
        self.log_area.setMaximumHeight(140)  # 限制最大高度
        layout.addWidget(self.log_area)

        # 添加执行按钮 - 减少尺寸
        self.execute_btn = QPushButton('批量提取秒退订单')
        self.execute_btn.setFont(QFont('Microsoft YaHei', 10, QFont.Weight.Bold))
        self.execute_btn.clicked.connect(self.start_processing)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                padding: 8px;
                border-radius: 4px;
                background-color: #1976D2;
                color: white;
                border: none;
                max-height: 36px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
        """)
        layout.addWidget(self.execute_btn)

    def append_log(self, text):
        """添加日志文本到日志区域"""
        self.log_area.append(text)
        # 滚动到底部
        self.log_area.verticalScrollBar().setValue(
            self.log_area.verticalScrollBar().maximum()
        )

    def toggle_all(self):
        """全选/取消全选功能"""
        all_checked = all(cb.isChecked() for cb in self.checkboxes)
        for checkbox in self.checkboxes:
            checkbox.setChecked(not all_checked)

    def start_processing(self):
        """开始处理选中的账号"""
        selected_accounts = [cb.text() for cb in self.checkboxes if cb.isChecked()]
        
        if not selected_accounts:
            QMessageBox.warning(self, '警告', '请至少选择一个账号！')
            return
        
        # 清空日志区域
        self.log_area.clear()
        self.append_log(f"选中了 {len(selected_accounts)} 个账号:")
        for account in selected_accounts:
            self.append_log(f"- {account}")
        
        # 禁用按钮，防止重复点击
        self.execute_btn.setEnabled(False)
        self.execute_btn.setText('正在处理...')
        
        # 创建工作线程
        self.worker = WorkerThread(selected_accounts)
        self.worker.progress.connect(self.append_log)
        self.worker.error.connect(self.handle_error)
        self.worker.finished.connect(self.on_processing_finished)
        self.worker.start()

    def handle_error(self, error_msg):
        """处理错误信息"""
        self.append_log(f"\n错误:\n{error_msg}")
        self.execute_btn.setEnabled(True)
        self.execute_btn.setText('批量提取秒退订单')
        QMessageBox.critical(self, '错误', f'处理过程中出现错误:\n{error_msg}')

    def on_processing_finished(self):
        """处理完成后的回调"""
        self.execute_btn.setEnabled(True)
        self.execute_btn.setText('批量提取秒退订单')
        self.append_log("\n处理完成！")

def main():
    if os.name == 'nt':
        try:
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('BatchRefundTool')
        except AttributeError:
            pass

    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main() 